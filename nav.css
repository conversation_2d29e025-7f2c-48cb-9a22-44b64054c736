.navbar-custom {
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    min-height: 60px;
}
.navbar-brand img {
    height: 32px;
    width: auto;
}
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: #333 !important;
    letter-spacing: 1px;
}
.navbar-nav .nav-link {
    color: #333 !important;
    font-weight: 400;
    font-size: 0.9rem;
    margin: 0 1rem;
    padding: 0.75rem 0 !important;
    transition: color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    height: 40px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.navbar-nav .nav-link:hover {
    color: #666 !important;
}
.navbar-nav .dropdown-toggle::after {
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

.navbar-icons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1.2rem;
    margin-left: auto;
    height: 40px;
}

.language-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    color: #333;
    font-size: 0.9rem;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.3s ease;
    height: 40px;
    padding: 0 0.5rem;
    min-width: auto;
}

.language-selector:hover {
    color: #666;
}

.lang-text {
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    line-height: 1;
}

.language-selector .bi-chevron-down {
    font-size: 0.7rem;
    margin-top: 1px;
}

.navbar-icons .icons {
    color: #333;
    font-size: 0.9rem;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0 0.5rem;
    min-width: auto;
}

.navbar-icons .icons:hover {
    color: #666;
}

.icons > .bi {
    flex-shrink: 0;
    width: 1.1em;
    height: 1.1em;
    fill: currentcolor;
    transition: .2s ease-in-out transform;
}

/* Dropdown menu styling */
.dropdown-menu {
    border: 1px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    color: #333;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #333;
}

/* Mobile navbar icons */
.navbar-icons-mobile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    order: 2;
}

.navbar-icons-mobile .language-selector,
.navbar-icons-mobile .icon-link {
    height: 32px;
    padding: 0 0.25rem;
    font-size: 0.85rem;
}

.navbar-icons-mobile .lang-text {
    font-size: 0.8rem;
}

.navbar-icons-mobile .bi-chevron-down {
    font-size: 0.6rem;
}

/* Custom navbar toggler */
.navbar-toggler {
    order: 3;
    border: none !important;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

/* Mobile responsiveness */
@media (max-width: 991.98px) {
    .navbar-custom {
        padding: 0.75rem 0;
    }

    .navbar-brand {
        order: 1;
    }

    .navbar-nav {
        text-align: left;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e0e0e0;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 0 !important;
        margin: 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 1rem;
    }

    .navbar-nav .nav-item:last-child .nav-link {
        border-bottom: none;
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: none;
        background-color: #f8f9fa;
        margin-left: 1rem;
        margin-top: 0.5rem;
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Hero Section */
.hero-section {
    margin-top: 60px; /* Account for fixed navbar */
    width: 100%;
    overflow: hidden;
}

.hero-content {
    position: relative;
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-svg {
    width: 100%;
    height: auto;
    max-width: 100%;
    display: block;
    object-fit: cover;
}

/* Tablet and larger screens */
@media (min-width: 992px) {
    .navbar-brand img {
        height: 32px;
    }

    .navbar-nav .nav-link {
        white-space: nowrap;
    }

    .hero-section {
        margin-top: 70px; /* Slightly more space on larger screens */
    }
}

/* Large screens */
@media (min-width: 1200px) {
    .hero-svg {
        max-height: 92vh; /* Limit height on very large screens */
        width: 100%;
        max-width: 100%;
    }
}

/* Mobile specific hero adjustments */
@media (max-width: 991.98px) {
    .hero-section {
        margin-top: 70px; /* Account for mobile navbar height */
    }

    .hero-svg {
        width: 100%;
        height: auto;
    }
}

/* Employee Plans Section */
.employee-plans-section {
    background-color: #ffffff;
    padding: 0;
}

.image-container {
    height: 100vh;
    overflow: hidden;
    position: relative;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.employee-image {
    width: 80%;
    height: 80%;
    max-width: 500px;
    max-height: 600px;
    object-fit: contain;
    object-position: center;
}

.content-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 2rem 3rem 4rem;
    background-color: #ffffff;
}

.section-title {
    font-size: 3.5rem;
    font-weight: 400;
    color: #333;
    margin-bottom: 2rem;
    line-height: 1;
    letter-spacing: -0.02em;
    font-family: 'Nuckle', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.section-description {
    font-size: 1rem;
    color:#141615;
    line-height: 1.7;
    margin-bottom: 2.5rem;
    font-weight: 300;
    max-width: 400px;
}

.discover-btn {
    display: inline-block;
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 400;
    padding: 0;
    transition: all 0.3s ease;
    align-self: flex-start;
    text-transform: none;
}

.discover-btn:hover {
    color: #666;
    border-bottom-color: #666;
}

/* Large screens - more space for image */
@media (min-width: 1400px) {
    .content-container {
        padding: 4rem 3rem 4rem 5rem;
    }

    .section-title {
        font-size: 3.6rem;
    }

    .employee-image {
        width: 75%;
        height: 75%;
        max-width: 600px;
        max-height: 700px;
    }
}

/* Mobile responsiveness for employee section */
@media (max-width: 991.98px) {
    .employee-plans-section .row {
        flex-direction: column;
    }

    .image-container {
        height: 80vh;
        min-height: 400px;
        padding: 1.5rem;
    }

    .employee-image {
        width: 90%;
        height: 90%;
        max-width: 400px;
        max-height: 500px;
    }

    .content-container {
        height: auto;
        min-height: 40vh;
        padding: 3rem 2rem 3rem 3rem;
        justify-content: center;
    }

    .section-title {
        font-size: 2.8rem;
        margin-bottom: 1.5rem;
    }

    .section-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        max-width: none;
    }
}

@media (max-width: 767.98px) {
    .image-container {
        height: 97vh;
        min-height: 300px;
    }

    .content-container {
        padding: 2.5rem 1.5rem;
        min-height: 50vh;
    }

    .section-title {
        font-size: 3.5rem;
        line-height: 1.2;
    }

    .section-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }
}

@media (max-width: 575.98px) {
    .content-container {
        padding: 2rem 1.25rem;
    }

    .section-title {
        font-size: 2.8rem;
    }
}

/* Service, Maintenance and Insurance Section */
.service-section {
    padding: 0;
    position: relative;
}

.service-background {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.service-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    z-index: 1;
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0 0 8%;
}

.service-content {
    max-width: 450px;
    color: white;
    z-index: 3;
}

.service-title {
    font-size: 3.6rem;
    font-weight: 300;
    color: #FFFFFF;
    margin-bottom: 2rem;
    line-height: 1;
    letter-spacing: -0.02em;
    font-family: 'Nuckle', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.service-description {
    font-size: 1rem;
    color: #FFFFFF;
    line-height: 1.6;
    margin-bottom: 0;
    font-weight: 300;
    max-width: 400px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Service Section Responsive Styles */
@media (min-width: 1400px) {
    .service-overlay {
        padding: 0 0 0 10%;
    }

    .service-title {
        font-size: 4.2rem;
        font-weight: 340;
    }

    .service-content {
        max-width: 500px;
    }

    .service-description {
        font-size: 1.1rem;
        max-width: 450px;
    }
    .charge-image{
    height: 90%;
    }
}

@media (max-width: 991.98px) {
    .service-background {
        height: 80vh;
        min-height: 500px;
    }

    .service-overlay {
        padding: 0 0 0 6%;
        align-items: center;
        justify-content: flex-start;
    }

    .service-title {
        font-size: 4rem;
        font-weight: 340;
        margin-bottom: 1.5rem;
    }

    .service-description {
        font-size: 1rem;
        max-width: 380px;
    }
    .charge-image{
    height: 390px;
    } 
}

@media (max-width: 767.98px) {
    .service-background {
        height: 70vh;
        min-height: 400px;
    }

    .service-overlay {
        padding: 0 0 0 5%;
    }

    .service-title {
        font-size: 3.4rem;
        font-weight: 340;
        line-height: 1.2;
        margin-bottom: 1.2rem;
    }

    .service-description {
        font-size: 0.95rem;
        line-height: 1.6;
        max-width: 320px;
    }
    .charge-image{
    height: 398px;
    }
}

@media (max-width: 575.98px) {
    .service-background {
        height: 60vh;
        min-height: 350px;
    }

    .service-overlay {
        padding: 0 0 0 6%;
    }

    .service-title {
        font-size: 2.8rem;
        font-weight: 340;
        margin-bottom: 1rem;
    }

    .service-description {
        font-size: 0.9rem;
        max-width: 280px;
    }
    .charge-image{
    height: 366px;
}
}
.cost-example-btn {
    display: inline-block;
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 400;
    padding: 0;
    transition: all 0.3s ease;
    align-self: flex-start;
    text-transform: none;
}

.cost-example-btn:hover {
    color: #666;
    border-bottom-color: #666;
}

/* How it Works Section Styles */
.how-it-works-section {
    background-color: #ffffff;
    padding: 0;
}

.how-it-works-section .image-container {
    height: 100vh;
    overflow: hidden;
    position: relative;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.how-it-works-image {
    width: 80%;
    height: 80%;
    max-width: 500px;
    max-height: 600px;
    object-fit: contain;
    object-position: center;
}

.how-it-works-section .content-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 2rem 3rem 4rem;
    background-color: #ffffff;
}

.how-it-works-section .section-title {
    font-size: 3.5rem;
    font-weight: 400;
    color: #333;
    margin-bottom: 3rem;
    line-height: 1;
    letter-spacing: -0.02em;
    font-family: 'Nuckle', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.step-item {
    margin-bottom: 2.5rem;
    display: flex;
    align-items: flex-start;
}

.step-number {
    flex-shrink: 0;
    margin-right: 1.5rem;
}

.number-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: #e9ecef;
    color: #333;
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
    line-height: 1.3;
}

.step-description {
    font-size: 1rem;
    color: #666;
    line-height: 1.7;
    margin-bottom: 0;
    font-weight: 300;
    max-width: 400px;
}

/* How it Works Section Responsive Styles */
@media (min-width: 1400px) {
    .how-it-works-section .content-container {
        padding: 4rem 3rem 4rem 5rem;
    }

    .how-it-works-section .section-title {
        font-size: 3.8rem;
        margin-bottom: 3.5rem;
    }

    .how-it-works-image {
        width: 75%;
        height: 75%;
        max-width: 600px;
        max-height: 700px;
    }

    .step-title {
        font-size: 1.5rem;
    }

    .step-description {
        font-size: 1.1rem;
        max-width: 450px;
    }
}

@media (max-width: 991.98px) {
    .how-it-works-section .row {
        flex-direction: column;
    }

    .how-it-works-section .image-container {
        height: 80vh;
        min-height: 400px;
        padding: 1.5rem;
        order: 2;
    }

    .how-it-works-image {
        width: 90%;
        height: 90%;
        max-width: 400px;
        max-height: 500px;
    }

    .how-it-works-section .content-container {
        height: auto;
        min-height: 60vh;
        padding: 3rem 2rem;
        justify-content: center;
        order: 1;
    }

    .how-it-works-section .section-title {
        font-size: 3rem;
        margin-bottom: 2.5rem;
        text-align: center;
    }

    .step-item {
        margin-bottom: 2rem;
    }

    .step-title {
        font-size: 1.3rem;
    }

    .step-description {
        font-size: 1rem;
        max-width: none;
    }
}

@media (max-width: 767.98px) {
    .how-it-works-section .image-container {
        height: 60vh;
        min-height: 300px;
    }

    .how-it-works-section .content-container {
        padding: 2.5rem 1.5rem;
        min-height: 70vh;
    }

    .how-it-works-section .section-title {
        font-size: 2.5rem;
        line-height: 1.2;
        margin-bottom: 2rem;
    }

    .number-circle {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .step-number {
        margin-right: 1.2rem;
    }

    .step-title {
        font-size: 1.2rem;
        margin-bottom: 0.6rem;
    }

    .step-description {
        font-size: 0.95rem;
        line-height: 1.6;
    }
}

@media (max-width: 575.98px) {
    .how-it-works-section .content-container {
        padding: 2rem 1.25rem;
        min-height: 80vh;
    }

    .how-it-works-section .section-title {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
    }

    .step-item {
        margin-bottom: 1.8rem;
    }

    .number-circle {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .step-number {
        margin-right: 1rem;
    }

    .step-title {
        font-size: 1.1rem;
    }

    .step-description {
        font-size: 0.9rem;
    }
}

/* Electric Bikes Section Styles */
.electric-bikes-section {
    background-color: #f8f9fa;
    padding: 80px 0;
}

.bikes-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.bike-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 500px;
    position: relative;
}

.bike-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.bike-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.bike-tag {
    background-color: #007bff;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bike-price {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    width: 100%;
    margin-top: 0.5rem;
}

.bike-image-container {
    position: relative;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.bike-image {
    max-width: 100%;
    max-height: 250px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e0e0e0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
}

.nav-btn:hover {
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.nav-btn-left {
    left: 1rem;
}

.nav-btn-right {
    right: 1rem;
}

.nav-btn i {
    font-size: 1rem;
    color: #333;
}

.bike-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #d0d0d0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dot.active {
    background-color: #333;
}

.dot:hover {
    background-color: #666;
}

/* Contact Section Styles - Minimalist */
.contact-section {
    background-color: #ffffff;
    padding: 60px 0;
}

.contact-form-container {
    background-color: transparent;
    padding: 2rem 1rem;
    border-radius: 0;
    max-width: 400px;
    margin: 0 auto;
    box-shadow: none;
}

.contact-title {
    font-size: 2rem;
    font-weight: 300;
    color: #333;
    text-align: center;
    margin-bottom: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.contact-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    font-size: 0.85rem;
    color: #888;
    margin-bottom: 0.3rem;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-input,
.contact-textarea,
.contact-select {
    border: none;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 0;
    padding: 0.5rem 0;
    font-size: 1rem;
    color: #333;
    background-color: transparent;
    transition: all 0.3s ease;
    width: 100%;
    font-weight: 300;
}

.contact-input:focus,
.contact-textarea:focus,
.contact-select:focus {
    border-bottom-color: #333;
    box-shadow: none;
    background-color: transparent;
    outline: none;
}

.contact-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23888' stroke-linecap='round' stroke-linejoin='round' stroke-width='1' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0 center;
    background-repeat: no-repeat;
    background-size: 1em 1em;
    padding-right: 1.5rem;
    cursor: pointer;
}

.contact-select option {
    color: #333;
    background-color: #fff;
    padding: 0.5rem;
}

.contact-textarea {
    resize: none;
    min-height: 60px;
    font-family: inherit;
}

.contact-submit-btn {
    background-color: #333;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    font-size: 0.8rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1.5rem;
}

.contact-submit-btn:hover {
    background-color: #555;
}

.contact-submit-btn:active {
    transform: none;
}

/* Contact Section Responsive Styles - Minimalist */
@media (min-width: 1400px) {
    .contact-form-container {
        padding: 3rem 2rem;
        max-width: 450px;
    }

    .contact-title {
        font-size: 2.2rem;
        margin-bottom: 2.5rem;
    }

    .form-label {
        font-size: 0.9rem;
    }

    .contact-input,
    .contact-textarea,
    .contact-select {
        font-size: 1.1rem;
        padding: 0.6rem 0;
    }

    .form-group {
        margin-bottom: 1.8rem;
    }

    .contact-submit-btn {
        padding: 0.8rem 2.5rem;
        font-size: 0.85rem;
        margin-top: 2rem;
    }
}

@media (max-width: 991.98px) {
    .contact-section {
        padding: 50px 0;
    }

    .contact-form-container {
        padding: 2rem 1rem;
        max-width: 380px;
    }

    .contact-title {
        font-size: 1.8rem;
        margin-bottom: 1.8rem;
    }

    .form-group {
        margin-bottom: 1.3rem;
    }
}

@media (max-width: 767.98px) {
    .contact-section {
        padding: 40px 0;
    }

    .contact-form-container {
        padding: 1.5rem 1rem;
        margin: 0 0.5rem;
        max-width: 350px;
    }

    .contact-title {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .contact-input,
    .contact-textarea,
    .contact-select {
        font-size: 0.95rem;
        padding: 0.4rem 0;
    }

    .form-group {
        margin-bottom: 1.2rem;
    }

    .contact-submit-btn {
        padding: 0.7rem 1.5rem;
        font-size: 0.75rem;
        margin-top: 1.2rem;
    }
}

@media (max-width: 575.98px) {
    .contact-form-container {
        padding: 1rem 0.75rem;
        margin: 0 0.25rem;
        max-width: 320px;
    }

    .contact-title {
        font-size: 1.4rem;
        margin-bottom: 1.2rem;
    }

    .form-label {
        font-size: 0.75rem;
        margin-bottom: 0.2rem;
    }

    .contact-input,
    .contact-textarea,
    .contact-select {
        font-size: 0.9rem;
        padding: 0.35rem 0;
    }

    .contact-select {
        padding-right: 1.2rem;
        background-size: 0.8em 0.8em;
    }

    .contact-textarea {
        min-height: 50px;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .contact-submit-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.7rem;
        margin-top: 1rem;
    }
}

/* FAQ Section Styles */
.faq-section {
    background-color: #ffffff;
    padding: 80px 0;
}

.faq-title {
    font-size: 3.5rem;
    font-weight: 400;
    color: #333;
    text-align: center;
    margin-bottom: 4rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
}

.faq-question:hover {
    color: #666;
}

.faq-question span {
    font-size: 1.3rem;
    font-weight: 400;
    color: #333;
    line-height: 1.4;
    flex: 1;
    margin-right: 1rem;
}

.faq-icon {
    font-size: 1.5rem;
    color: #333;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.faq-item.active .faq-icon {
    transform: rotate(0deg);
}

.faq-answer {
    padding: 0 0 2rem 0;
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
    max-width: 90%;
}

/* Bootstrap collapse override */
.faq-item .collapse {
    transition: all 0.3s ease;
}

.faq-item .collapsing {
    transition: height 0.3s ease;
}

/* FAQ Section Responsive Styles */
@media (min-width: 1400px) {
    .faq-title {
        font-size: 4rem;
        margin-bottom: 5rem;
    }

    .faq-question span {
        font-size: 1.4rem;
    }

    .faq-answer {
        font-size: 1.1rem;
        max-width: 85%;
    }

    .faq-question {
        padding: 2.5rem 0;
    }

    .faq-answer {
        padding: 0 0 2.5rem 0;
    }
}

@media (max-width: 991.98px) {
    .faq-section {
        padding: 60px 0;
    }

    .faq-title {
        font-size: 3rem;
        margin-bottom: 3rem;
    }

    .faq-question span {
        font-size: 1.2rem;
    }

    .faq-answer {
        font-size: 1rem;
        max-width: 95%;
    }

    .faq-question {
        padding: 1.5rem 0;
    }

    .faq-answer {
        padding: 0 0 1.5rem 0;
    }
}

@media (max-width: 767.98px) {
    .faq-section {
        padding: 50px 0;
    }

    .faq-title {
        font-size: 2.5rem;
        margin-bottom: 2.5rem;
    }

    .faq-question span {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .faq-answer {
        font-size: 0.95rem;
        max-width: 100%;
        line-height: 1.5;
    }

    .faq-question {
        padding: 1.25rem 0;
    }

    .faq-answer {
        padding: 0 0 1.25rem 0;
    }

    .faq-icon {
        font-size: 1.3rem;
    }
}

@media (max-width: 575.98px) {
    .faq-section {
        padding: 40px 0;
    }

    .faq-title {
        font-size: 2.2rem;
        margin-bottom: 2rem;
    }

    .faq-question span {
        font-size: 1rem;
        margin-right: 0.5rem;
    }

    .faq-answer {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .faq-question {
        padding: 1rem 0;
    }

    .faq-answer {
        padding: 0 0 1rem 0;
    }

    .faq-icon {
        font-size: 1.2rem;
    }
}

/* Footer Section Styles */
.footer-section {
    background-color: #000000;
    color: #ffffff;
}

.footer-banner {
    background-color: #666666;
    padding: 1.5rem 0;
}

.banner-text {
    font-size: 1rem;
    color: #ffffff;
    margin: 0;
    line-height: 1.5;
}

.footer-btn {
    color: #000000 !important;
    border: none;
    padding: 0.75rem 2rem;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.footer-btn:hover {
    background-color: #5a6268 !important;
    color: #000000 !important;
    transform: translateY(-1px);
}

.footer-main {
    padding: 4rem 0 2rem 0;
}

.footer-heading {
    font-size: 1.1rem;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #cccccc;
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ffffff;
}

.newsletter-text {
    color: #cccccc;
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
}

.newsletter-form {
    margin-bottom: 1rem;
}

.newsletter-input-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.newsletter-input {
    background-color: transparent;
    border: 2px solid #ffffff;
    color: #ffffff;
    padding: 1.2rem 180px 1.2rem 2rem;
    font-size: 1rem;
    width: 100%;
    border-radius: 50px !important;
    height: auto;
}

.newsletter-input::placeholder {
    color: #cccccc;
    font-size: 1rem;
}

.newsletter-input:focus {
    background-color: transparent;
    border-color: #ffffff;
    box-shadow: none;
    color: #ffffff;
    outline: none;
}

.newsletter-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ffffff;
    color: #333;
    border: none;
    border-radius: 50px;
    padding: 1rem 2.5rem;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    z-index: 2;
    height: calc(100% - 16px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.newsletter-btn:hover {
    background-color: #f0f0f0;
    color: #333;
}

.newsletter-checkbox {
    margin-bottom: 2rem;
}

.newsletter-checkbox .form-check-input {
    background-color: transparent;
    border-color: #555;
    margin-top: 0.2rem;
}

.newsletter-checkbox .form-check-input:checked {
    background-color: #ffffff;
    border-color: #ffffff;
}

.newsletter-checkbox .form-check-label {
    color: #cccccc;
    font-size: 0.85rem;
    line-height: 1.4;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-link {
    color: #cccccc;
    font-size: 1.2rem;
    transition: color 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    color: #ffffff;
}

.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.btn-back-to-top {
    background-color: #ffffff;
    color: #333;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.btn-back-to-top:hover {
    background-color: #f0f0f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Footer Responsive Styles */
@media (max-width: 991.98px) {
    .footer-banner {
        padding: 1.25rem 0;
    }

    .banner-text {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .footer-btn {
        padding: 0.7rem 1.8rem;
        font-size: 0.85rem;
    }

    .footer-main {
        padding: 3rem 0 1.5rem 0;
    }

    .footer-heading {
        font-size: 1rem;
        margin-bottom: 1.25rem;
    }

    .footer-links a {
        font-size: 0.9rem;
    }

    .newsletter-text {
        font-size: 0.9rem;
        margin-bottom: 1.25rem;
    }
}

@media (max-width: 767.98px) {
    .footer-banner {
        padding: 1rem 0;
        text-align: center;
    }

    .banner-text {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .footer-btn {
        padding: 0.65rem 1.5rem;
        font-size: 0.8rem;
    }

    .footer-main {
        padding: 2.5rem 0 1.25rem 0;
    }

    .footer-heading {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .footer-links li {
        margin-bottom: 0.6rem;
    }

    .footer-links a {
        font-size: 0.85rem;
    }

    .newsletter-text {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .newsletter-input-container {
        max-width: 450px;
    }

    .newsletter-input {
        font-size: 0.95rem;
        padding: 1rem 160px 1rem 1.75rem;
    }

    .newsletter-btn {
        font-size: 0.95rem;
        padding: 0.85rem 2rem;
        right: 6px;
    }

    .newsletter-checkbox .form-check-label {
        font-size: 0.8rem;
    }

    .social-icons {
        gap: 0.8rem;
    }

    .social-link {
        font-size: 1.1rem;
    }

    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
    }

    .btn-back-to-top {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 575.98px) {
    .footer-banner {
        padding: 0.8rem 0;
    }

    .banner-text {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .footer-btn {
        padding: 0.6rem 1.25rem;
        font-size: 0.75rem;
        margin-top: 0.5rem;
    }

    .footer-main {
        padding: 2rem 0 1rem 0;
    }

    .footer-heading {
        font-size: 0.9rem;
        margin-bottom: 0.8rem;
    }

    .footer-links li {
        margin-bottom: 0.5rem;
    }

    .footer-links a {
        font-size: 0.8rem;
    }

    .newsletter-text {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .newsletter-input-container {
        max-width: 350px;
    }

    .newsletter-input {
        font-size: 0.9rem;
        padding: 0.9rem 140px 0.9rem 1.5rem;
        border-width: 1.5px;
    }

    .newsletter-btn {
        font-size: 0.85rem;
        padding: 0.7rem 1.5rem;
        right: 5px;
    }

    .newsletter-checkbox {
        margin-bottom: 1.5rem;
    }

    .newsletter-checkbox .form-check-label {
        font-size: 0.75rem;
    }

    .social-icons {
        gap: 0.6rem;
    }

    .social-link {
        font-size: 1rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
    }

    .btn-back-to-top {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Electric Bikes Section Responsive Styles */
@media (min-width: 1400px) {
    .bikes-container {
        gap: 3rem;
        max-width: 1400px;
    }

    .bike-card {
        padding: 2.5rem;
    }

    .bike-name {
        font-size: 1.6rem;
    }

    .bike-price {
        font-size: 1.1rem;
    }

    .bike-image-container {
        min-height: 350px;
        padding: 2.5rem;
    }

    .bike-image {
        max-height: 300px;
    }
}

@media (max-width: 991.98px) {
    .bikes-container {
        flex-direction: column;
        gap: 2rem;
        max-width: 600px;
    }

    .bike-card {
        max-width: none;
        padding: 1.5rem;
    }

    .bike-header {
        justify-content: flex-start;
        gap: 1rem;
    }

    .bike-name {
        font-size: 1.4rem;
    }

    .bike-tag {
        order: 2;
    }

    .bike-price {
        order: 3;
        margin-top: 0.5rem;
        font-size: 1rem;
    }

    .bike-image-container {
        min-height: 280px;
        padding: 1.5rem;
    }

    .bike-image {
        max-height: 220px;
    }
}

@media (max-width: 767.98px) {
    .electric-bikes-section {
        padding: 60px 0;
    }

    .bikes-container {
        padding: 0 1rem;
        gap: 1.5rem;
    }

    .bike-card {
        padding: 1.25rem;
    }

    .bike-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .bike-name {
        font-size: 1.3rem;
    }

    .bike-tag {
        align-self: flex-start;
    }

    .bike-price {
        margin-top: 0.25rem;
        font-size: 0.95rem;
    }

    .bike-image-container {
        min-height: 250px;
        padding: 1.25rem;
    }

    .bike-image {
        max-height: 200px;
    }

    .nav-btn {
        width: 35px;
        height: 35px;
    }

    .nav-btn-left {
        left: 0.75rem;
    }

    .nav-btn-right {
        right: 0.75rem;
    }

    .nav-btn i {
        font-size: 0.9rem;
    }
}

@media (max-width: 575.98px) {
    .electric-bikes-section {
        padding: 50px 0;
    }

    .bikes-container {
        padding: 0 0.75rem;
    }

    .bike-card {
        padding: 1rem;
    }

    .bike-name {
        font-size: 1.2rem;
    }

    .bike-price {
        font-size: 0.9rem;
    }

    .bike-image-container {
        min-height: 220px;
        padding: 1rem;
    }

    .bike-image {
        max-height: 180px;
    }

    .nav-btn {
        width: 32px;
        height: 32px;
    }

    .nav-btn i {
        font-size: 0.8rem;
    }

    .dot {
        width: 8px;
        height: 8px;
    }
}